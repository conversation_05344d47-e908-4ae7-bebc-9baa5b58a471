<x-admin.app :title="$page_title" :from_url="route('admin.dashboard')">

    <x-slot name="breadcrumbSlot">
        <a href="{{ route('admin.certifications.create') }}" class="btn btn-primary">
            <i class="fa fa-plus"></i>
            <span>Add New Certification</span>
        </a>
    </x-slot>

    <div class="card">
        <div class="card-header">
            <h4 class="card-title">
                <div class="row justify-content-end">
                    <div class="col-md-4">
                        <div class="form-group">
                            <input type="text" class="form-control" placeholder="Search..." name="search" id="searchInput" value="{{ $request->get('search') }}">
                        </div>
                    </div>
                    <div class="col-md-4">
                        <select class="form-control" id="upworkIdFilter">
                            <option value="">All Upwork IDs</option>
                            @foreach($upworkIds as $upworkId)
                                <option value="{{ $upworkId->id }}" {{ $request->get('upwork_id') == $upworkId->id ? 'selected' : '' }}>
                                    {{ $upworkId->upwork_id }}
                                </option>
                            @endforeach
                        </select>
                    </div>
                </div>
            </h4>
        </div>
        <div class="card-body">
            <div class="table-responsive" id="data-container">
                <table class="table table-bordered table-striped table-hover" id="certification-table">
                    <thead>
                    <tr>
                        <th>SL#</th>
                        <th>Certification Title</th>
                        <th>Upwork ID</th>
                        <th>User</th>
                        <th>Certification Date</th>
                        <th>File</th>
                        <th class="text-center">Action</th>
                    </tr>
                    </thead>
                    <tbody>
                    @forelse($certifications as $certification)
                        <tr class="editable">
                            <td>{{ $certifications->firstItem() + $loop->index }}</td>
                            <td>{{ $certification->certification_title ?? 'N/A' }}</td>
                            <td>{{ $certification->upworkID->upwork_id ?? 'N/A' }}</td>
                            <td>{{ $certification->owner->name ?? 'N/A' }}</td>
                            <td>{{ $certification->certification_date ? $certification->certification_date->format('M d, Y') : 'N/A' }}</td>
                            <td>
                                @if($certification->certification_upload)
                                    <a href="{{ Storage::url($certification->certification_upload) }}" target="_blank" class="btn btn-sm btn-info">
                                        <i class="fa fa-download"></i> View
                                    </a>
                                @else
                                    N/A
                                @endif
                            </td>
                            <td class="text-center">
                                <a href="{{ route('admin.certifications.show', $certification->id) }}" class="btn btn-sm btn-info">
                                    <i class="fa fa-eye"></i>
                                </a>
                                <a href="{{ route('admin.certifications.edit', $certification->id) }}" class="btn btn-sm btn-warning">
                                    <i class="fa fa-edit"></i>
                                </a>
                                <a href="{{ route('admin.certifications.destroy', $certification->id) }}"
                                    class="btn btn-danger"
                                    onclick="return makeDeleteRequest(event, {{ $certification->id }})">
                                    <i class="fa fa-trash"></i>
                                    <x-form action="{{ route('admin.certifications.destroy', $certification->id) }}"
                                            id="delete-form-{{ $certification->id }}" style="display: none;"
                                            method="DELETE">
                                    </x-form>
                                </a>
                            </td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="7" class="text-center">No certifications found.</td>
                        </tr>
                    @endforelse
                    </tbody>
                </table>
            </div>
            
            <div class="d-flex justify-content-center">
                {{ $certifications->links() }}
            </div>
        </div>
    </div>

    <x-slot:bottomScripts>
        <script type="text/javascript">
            $(document).ready(function () {
                // Search functionality
                $('#searchInput').on('keyup', function() {
                    let search = $(this).val();
                    let upworkId = $('#upworkIdFilter').val();
                    
                    $.ajax({
                        url: "{{ route('admin.certifications.index') }}",
                        type: 'GET',
                        data: {
                            search: search,
                            upwork_id: upworkId
                        },
                        success: function(response) {
                            $('#data-container').html(response);
                        }
                    });
                });

                // Filter functionality
                $('#upworkIdFilter').on('change', function() {
                    let upworkId = $(this).val();
                    let search = $('#searchInput').val();
                    
                    window.location.href = "{{ route('admin.certifications.index') }}?" + 
                        (upworkId ? 'upwork_id=' + upworkId : '') + 
                        (search ? '&search=' + search : '');
                });
            });
        </script>
    </x-slot>

</x-admin.app>
