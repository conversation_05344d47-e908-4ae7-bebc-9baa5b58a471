<table class="table table-bordered table-striped table-hover" id="certification-table">
    <thead>
    <tr>
        <th>SL#</th>
        <th>Certification Title</th>
        <th>Upwork ID</th>
        <th>Certification Date</th>
        <th>File</th>
        <th class="text-center">Action</th>
    </tr>
    </thead>
    <tbody>
    <?php $__empty_1 = true; $__currentLoopData = $certifications; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $certification): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
        <tr>
            <td><?php echo e($certifications->firstItem() + $loop->index); ?></td>
            <td><?php echo e($certification->certification_title ?? 'N/A'); ?></td>
            <td><?php echo e($certification->upworkID->upwork_id ?? 'N/A'); ?></td>
            <td><?php echo e($certification->certification_date ? $certification->certification_date->format('M d, Y') : 'N/A'); ?></td>
            <td>
                <?php if($certification->certification_upload): ?>
                    <a href="<?php echo e(Storage::url($certification->certification_upload)); ?>" target="_blank" class="btn btn-sm btn-info">
                        <i class="fa fa-download"></i> View
                    </a>
                <?php else: ?>
                    N/A
                <?php endif; ?>
            </td>
            <td class="text-center">
                <a href="<?php echo e(route('certifications.show', $certification->id)); ?>" class="btn btn-sm btn-info">
                    <i class="fa fa-eye"></i>
                </a>
                <a href="<?php echo e(route('certifications.edit', $certification->id)); ?>" class="btn btn-sm btn-warning">
                    <i class="fa fa-edit"></i>
                </a>
                <a href="<?php echo e(route('certifications.destroy', $certification->id)); ?>"
                    class="btn btn-danger"
                    onclick="return makeDeleteRequest(event, <?php echo e($certification->id); ?>)">
                    <i class="fa fa-trash"></i>
                    <?php if (isset($component)) { $__componentOriginalf9a5f060e1fbbcbc7beb643b113b10ab = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalf9a5f060e1fbbcbc7beb643b113b10ab = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.form','data' => ['action' => ''.e(route('certifications.destroy', $certification->id)).'','id' => 'delete-form-'.e($certification->id).'','style' => 'display: none;','method' => 'DELETE']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('form'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['action' => ''.e(route('certifications.destroy', $certification->id)).'','id' => 'delete-form-'.e($certification->id).'','style' => 'display: none;','method' => 'DELETE']); ?>
                     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalf9a5f060e1fbbcbc7beb643b113b10ab)): ?>
<?php $attributes = $__attributesOriginalf9a5f060e1fbbcbc7beb643b113b10ab; ?>
<?php unset($__attributesOriginalf9a5f060e1fbbcbc7beb643b113b10ab); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalf9a5f060e1fbbcbc7beb643b113b10ab)): ?>
<?php $component = $__componentOriginalf9a5f060e1fbbcbc7beb643b113b10ab; ?>
<?php unset($__componentOriginalf9a5f060e1fbbcbc7beb643b113b10ab); ?>
<?php endif; ?>
                </a>
            </td>
        </tr>
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
        <tr>
            <td colspan="6" class="text-center">No certifications found.</td>
        </tr>
    <?php endif; ?>
    </tbody>
</table>

<div class="d-flex justify-content-center">
    <?php echo e($certifications->links()); ?>

</div>
<?php /**PATH D:\xampp8.2\htdocs\portfolio\resources\views/user/certifications/table_response.blade.php ENDPATH**/ ?>