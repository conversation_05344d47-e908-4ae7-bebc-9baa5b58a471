<?php

namespace App\Http\Requests\Admin\Certification;

use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;

class CertificationRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array|string>
     */
    public function rules(): array
    {
        $rules = [
            'upwork_id' => 'bail|required|integer|exists:upwork_id,id',
            'certification_title' => 'bail|nullable|string|max:255',
            'certification_date' => 'bail|nullable|date',
            'user_id' => 'bail|required|integer|exists:users,id',
        ];

        // File upload validation - required for create, optional for update
        if ($this->isMethod('post')) {
            $rules['certification_upload'] = 'bail|required|file|mimes:pdf,jpg,jpeg,png,doc,docx|max:10240'; // 10MB max
        } else {
            $rules['certification_upload'] = 'bail|nullable|file|mimes:pdf,jpg,jpeg,png,doc,docx|max:10240'; // 10MB max
        }

        return $rules;
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'upwork_id.required' => 'Please select an Upwork ID.',
            'upwork_id.exists' => 'The selected Upwork ID is invalid.',
            'certification_upload.required' => 'Please upload a certification file.',
            'certification_upload.mimes' => 'The certification file must be a PDF, JPG, PNG, DOC, or DOCX file.',
            'certification_upload.max' => 'The certification file must not be larger than 10MB.',
            'user_id.required' => 'Please select a user.',
            'user_id.exists' => 'The selected user is invalid.',
            'certification_date.date' => 'Please enter a valid certification date.',
        ];
    }
}
