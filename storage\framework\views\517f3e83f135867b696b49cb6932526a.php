<?php if (isset($component)) { $__componentOriginalf56340035eeb7de26edb3ee88689cd6b = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalf56340035eeb7de26edb3ee88689cd6b = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.user.app','data' => ['title' => $page_title,'fromUrl' => route('portfolios.index')]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('user.app'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['title' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($page_title),'from_url' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(route('portfolios.index'))]); ?>

     <?php $__env->slot('breadcrumbSlot', null, []); ?> 
        <a href="<?php echo e(route('portfolios.index')); ?>" class="btn btn-dark">
            <i class="fa fa-arrow-left"></i>
            <span>Back</span>
        </a>

        <?php if($portfolio->user_id == auth()->id()): ?>
            <a href="<?php echo e(route('shortPortfolio.edit', $portfolio->id)); ?>" class="btn btn-secondary">
                <i class="fa fa-edit"></i>
                Edit
            </a>
            <a href="<?php echo e(route('portfolios.destroy', $portfolio->id)); ?>" class="btn btn-danger"
               onclick="return makeDeleteRequest(event, <?php echo e($portfolio->id); ?>)">
                Delete
                <i class="fa fa-trash"></i>
                <?php if (isset($component)) { $__componentOriginalf9a5f060e1fbbcbc7beb643b113b10ab = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalf9a5f060e1fbbcbc7beb643b113b10ab = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.form','data' => ['action' => ''.e(route('portfolios.destroy', $portfolio->id)).'','id' => 'delete-form-'.e($portfolio->id).'','style' => 'display: none;','method' => 'DELETE']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('form'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['action' => ''.e(route('portfolios.destroy', $portfolio->id)).'','id' => 'delete-form-'.e($portfolio->id).'','style' => 'display: none;','method' => 'DELETE']); ?>
                 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalf9a5f060e1fbbcbc7beb643b113b10ab)): ?>
<?php $attributes = $__attributesOriginalf9a5f060e1fbbcbc7beb643b113b10ab; ?>
<?php unset($__attributesOriginalf9a5f060e1fbbcbc7beb643b113b10ab); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalf9a5f060e1fbbcbc7beb643b113b10ab)): ?>
<?php $component = $__componentOriginalf9a5f060e1fbbcbc7beb643b113b10ab; ?>
<?php unset($__componentOriginalf9a5f060e1fbbcbc7beb643b113b10ab); ?>
<?php endif; ?>
            </a>
        <?php endif; ?>
     <?php $__env->endSlot(); ?>

    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-body">
                    <table class="table table-hover table-bordered table-striped text-center">
                        <?php if($portfolio->site_url): ?>
                            <tr>
                                <th>Site URL</th>
                                <td>
                                    <a href="<?php echo e(url($portfolio->site_url)); ?>" target="_blank" id="siteUrl">
                                        <?php echo e($portfolio->site_url); ?>

                                    </a>
                                </td>
                            </tr>
                        <?php endif; ?>
                        <tr>
                            <th>Project Name</th>
                            <td id="projectName"><?php echo e($portfolio->project_name); ?></td>
                        </tr>

                        <?php if($portfolio->client_type): ?>
                            <tr>
                                <th>Client Type</th>
                                <td id="clientType"><?php echo e(\App\Models\Portfolio::$clientTypeOptions[$portfolio->client_type]); ?></td>
                            </tr>
                        <?php endif; ?>

                        <?php if($portfolio->client_type == \App\Models\Portfolio::CLIENT_TYPE_CLIENT_REF): ?>
                            <?php if($portfolio->client_name_ref): ?>
                                <tr>
                                    <th>Client Reference Name</th>
                                    <td id="clientRefName"><?php echo e($portfolio->client_name_ref); ?></td>
                                </tr>
                            <?php endif; ?>

                            <?php if($portfolio->client_ref_type): ?>
                                <tr>
                                    <th>Client Reference Type</th>
                                    <td id="clientRefType"><?php echo e(\App\Models\Portfolio::$clientRefTypeOptions[$portfolio->client_ref_type]); ?></td>
                                </tr>
                            <?php endif; ?>

                            <?php if($portfolio->contract_type): ?>
                                <tr>
                                    <th>Contract Type</th>
                                    <td id="contractType"><?php echo e(\App\Models\Portfolio::$contractTypeOptions[$portfolio->contract_type]); ?></td>
                                </tr>
                            <?php endif; ?>

                            <?php if($portfolio->contract_type_rate): ?>
                                <tr>
                                    <th>Contract Type Rate</th>
                                    <td id="contractTypeRate">$<?php echo e(number_format($portfolio->contract_type_rate, 2)); ?></td>
                                </tr>
                            <?php endif; ?>
                        <?php endif; ?>

                        <?php if($portfolio->upwork_id): ?>
                            <tr>
                                <th>Upwork ID</th>
                                <td id="upworkId">
                                    <?php echo e($portfolio->upworkID->upwork_id); ?>

                                    <?php if(isset($portfolio->other_values['upwork_id_other'])): ?>
                                        <?php echo e(' - ' . @$portfolio->other_values['upwork_id_other']); ?>

                                    <?php endif; ?>
                                </td>
                            </tr>
                        <?php endif; ?>

                        <tr>
                            <th>Resources Name</th>
                            <td id="developers">
                                <?php $__empty_1 = true; $__currentLoopData = $portfolio->developers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $developer): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                    <span class="badge badge-pill">
                                        <?php echo e($developer->developer); ?>

                                    </span>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                    <?php echo e('N/A'); ?>

                                <?php endif; ?>
                            </td>
                        </tr>

                        <tr>
                            <th>Project Managers</th>
                            <td id="projectManagers">
                                <?php $__empty_1 = true; $__currentLoopData = $portfolio->projectManagers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $projectManager): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                    <span class="badge badge-pill">
                                        <?php echo e($projectManager->project_manager); ?>

                                    </span>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                    <?php echo e('N/A'); ?>

                                <?php endif; ?>
                            </td>
                        </tr>

                        <tr>
                            <th>Business Analysts</th>
                            <td id="businessAnalyst">
                                <?php $__empty_1 = true; $__currentLoopData = $portfolio->businessAnalysts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $businessAnalyst): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                    <span class="badge badge-pill">
                                        <?php echo e($businessAnalyst->business_analyst); ?>

                                    </span>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                    <?php echo e('N/A'); ?>

                                <?php endif; ?>
                            </td>
                        </tr>

                        <tr>
                            <th>Technologies</th>
                            <td id="technologies">
                                <?php $__empty_1 = true; $__currentLoopData = $portfolio->technologies; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $technology): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                    <span class="badge badge-pill">
                                        <?php echo e($technology->technology); ?>

                                    </span>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                    <?php echo e('N/A'); ?>

                                <?php endif; ?>
                            </td>
                        </tr>

                        <tr>
                            <th>Created By</th>
                            <td id="createdBy">
                                <?php echo e($portfolio->user_name); ?> on <?php echo e($portfolio->updated_at->format('jS M, Y \a\t h:ia')); ?>

                            </td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
    </div>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalf56340035eeb7de26edb3ee88689cd6b)): ?>
<?php $attributes = $__attributesOriginalf56340035eeb7de26edb3ee88689cd6b; ?>
<?php unset($__attributesOriginalf56340035eeb7de26edb3ee88689cd6b); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalf56340035eeb7de26edb3ee88689cd6b)): ?>
<?php $component = $__componentOriginalf56340035eeb7de26edb3ee88689cd6b; ?>
<?php unset($__componentOriginalf56340035eeb7de26edb3ee88689cd6b); ?>
<?php endif; ?>
<?php /**PATH D:\xampp8.2\htdocs\portfolio\resources\views/user/portfolios/short-portfolio-show.blade.php ENDPATH**/ ?>