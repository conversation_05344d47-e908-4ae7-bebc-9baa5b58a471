<x-user.app :title="$page_title" :from_url="route('dashboard')">

    <x-slot name="breadcrumbSlot">
        <a href="{{ route('certifications.create') }}" class="btn btn-primary">
            <i class="fa fa-plus"></i>
            <span>Add New Certification</span>
        </a>
    </x-slot>

    <div class="card">
        <div class="card-header">
            <h4 class="card-title">
                <div class="row justify-content-end">
                    <div class="col-md-4">
                        <div class="form-group">
                            <input type="text" class="form-control" placeholder="Search..." name="search" id="searchInput" value="{{ $request->get('search') }}">
                        </div>
                    </div>
                    <div class="col-md-4">
                        <select class="form-control" id="upworkIdFilter">
                            <option value="">All Upwork IDs</option>
                            @foreach($upworkIds as $upworkId)
                                <option value="{{ $upworkId->id }}" {{ $request->get('upwork_id') == $upworkId->id ? 'selected' : '' }}>
                                    {{ $upworkId->upwork_id }}
                                </option>
                            @endforeach
                        </select>
                    </div>
                </div>
            </h4>
        </div>
        <div class="card-body">
            <div class="table-responsive" id="data-container">
                @include('user.certifications.table_response')
            </div>
        </div>
    </div>

    <x-slot:bottomScripts>
        <script type="text/javascript">
            $(document).ready(function () {
                // Search functionality
                $('#searchInput').on('keyup', function() {
                    let search = $(this).val();
                    let upworkId = $('#upworkIdFilter').val();
                    
                    $.ajax({
                        url: "{{ route('certifications.index') }}",
                        type: 'GET',
                        data: {
                            search: search,
                            upwork_id: upworkId
                        },
                        success: function(response) {
                            $('#data-container').html(response.data);
                        }
                    });
                });

                // Filter functionality
                $('#upworkIdFilter').on('change', function() {
                    let upworkId = $(this).val();
                    let search = $('#searchInput').val();
                    
                    window.location.href = "{{ route('certifications.index') }}?" + 
                        (upworkId ? 'upwork_id=' + upworkId : '') + 
                        (search ? '&search=' + search : '');
                });
            });
        </script>
    </x-slot>

</x-user.app>
