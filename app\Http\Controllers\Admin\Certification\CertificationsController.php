<?php

namespace App\Http\Controllers\Admin\Certification;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\Certification\CertificationRequest;
use App\Models\Certification;
use App\Models\UpworkId;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class CertificationsController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $page_title = 'Certifications';
        $search = $request->get('search');
        $page = $request->get('page', 1);
        $upworkId = $request->get('upwork_id');

        $certifications = Certification::query()->latest()->with([
            'upworkID',
            'owner'
        ]);

        if ($search) {
            $certifications->where(function ($query) use ($search) {
                $query->where('certification_title', 'like', '%' . $search . '%')
                    ->orWhereHas('upworkID', function ($query) use ($search) {
                        $query->where('upwork_id', 'like', '%' . $search . '%');
                    })
                    ->orWhereHas('owner', function ($query) use ($search) {
                        $query->where('name', 'like', '%' . $search . '%');
                    });
            });
        }

        if ($upworkId) {
            $certifications->where('upwork_id', $upworkId);
        }

        $certificationCount = Certification::count();
        $certificationsAddedToday = Certification::whereDate('created_at', today())->count();

        if ($certificationsAddedToday) {
            $finalCertification = $certificationCount - $certificationsAddedToday;
        } else {
            $finalCertification = $certificationCount;
        }

        $upworkIds = UpworkId::latest()->get();

        $certifications = $certifications->paginate(20, ['*'], 'page', $page);

        return view('admin.certifications.index', compact(
            'page_title',
            'certifications',
            'request',
            'certificationsAddedToday',
            'finalCertification',
            'certificationCount',
            'upworkIds'
        ));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $page_title = 'Create Certification';
        $upworkIds = UpworkId::latest()->get();
        $users = User::where('role_id', '!=', 1)->latest()->get(); // Exclude admin users

        return view('admin.certifications.create', compact(
            'page_title',
            'upworkIds',
            'users'
        ));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(CertificationRequest $request)
    {
        $data = $request->validated();

        // Handle file upload
        if ($request->hasFile('certification_upload')) {
            $data['certification_upload'] = imageUploadHandler($request->file('certification_upload'), 'certifications');
        }

        // Set user_id if not provided (admin creating for user)
        if (!isset($data['user_id'])) {
            $data['user_id'] = auth()->id();
        }

        Certification::create($data);

        return redirect()->route('admin.certifications.index')
            ->with('success', 'Certification created successfully.');
sendFlash('Certification updated successfully.');
return to_route('admin.certifications.index');
    }

    /**
     * Display the specified resource.
     */
    public function show(Certification $certification)
    {
        $page_title = 'View Certification';
        $certification->load(['upworkID', 'owner']);

        return view('admin.certifications.show', compact('page_title', 'certification'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Certification $certification)
    {
        $page_title = 'Edit Certification';
        $upworkIds = UpworkId::latest()->get();
        $users = User::where('role_id', '!=', 1)->latest()->get(); // Exclude admin users

        return view('admin.certifications.edit', compact(
            'page_title',
            'certification',
            'upworkIds',
            'users'
        ));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(CertificationRequest $request, Certification $certification)
    {
        $data = $request->validated();

        // Handle file upload
        if ($request->hasFile('certification_upload')) {
            // Delete old file if exists
            if ($certification->certification_upload && Storage::disk('public')->exists($certification->certification_upload)) {
                Storage::disk('public')->delete($certification->certification_upload);
            }

            $file = $request->file('certification_upload');
            $fileName = time() . '_' . $file->getClientOriginalName();
            $filePath = $file->storeAs('certifications', $fileName, 'public');
            $data['certification_upload'] = $filePath;
        }

        $certification->update($data);
        sendFlash('Certification updated successfully.');
        return to_route('admin.certifications.index');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Certification $certification)
    {
        // Delete associated file
        if ($certification->certification_upload && Storage::disk('public')->exists($certification->certification_upload)) {
            Storage::disk('public')->delete($certification->certification_upload);
        }

        $certification->delete();

        sendFlash('Certification deleted successfully.');
        return to_route('admin.certifications.index');
    }
}
