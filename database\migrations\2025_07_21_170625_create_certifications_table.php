<?php

use App\Models\User;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('certifications', function (Blueprint $table) {
            $table->id();
            $table->foreignIdFor(User::class)
                ->comment('User id of certification owner')
                ->constrained()->cascadeOnDelete();
            $table->foreignId('upwork_id')->constrained('upwork_id')->cascadeOnDelete();
            $table->string('certification_title')->nullable()->comment('Title of the certification');
            $table->date('certification_date')->nullable()->comment('Date when certification was obtained');
            $table->string('certification_upload')->comment('File path for certification document (PDF, JPG, PNG, DOC)');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('certifications');
    }
};
