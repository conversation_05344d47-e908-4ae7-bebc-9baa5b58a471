<table class="table table-bordered table-striped table-hover" id="certification-table">
    <thead>
    <tr>
        <th>SL#</th>
        <th>Certification Title</th>
        <th>Upwork ID</th>
        <th>Certification Date</th>
        <th>File</th>
        <th class="text-center">Action</th>
    </tr>
    </thead>
    <tbody>
    @forelse($certifications as $certification)
        <tr>
            <td>{{ $certifications->firstItem() + $loop->index }}</td>
            <td>{{ $certification->certification_title ?? 'N/A' }}</td>
            <td>{{ $certification->upworkID->upwork_id ?? 'N/A' }}</td>
            <td>{{ $certification->certification_date ? $certification->certification_date->format('M d, Y') : 'N/A' }}</td>
            <td>
                @if($certification->certification_upload)
                    <a href="{{ Storage::url($certification->certification_upload) }}" target="_blank" class="btn btn-sm btn-info">
                        <i class="fa fa-download"></i> View
                    </a>
                @else
                    N/A
                @endif
            </td>
            <td class="text-center">
                <a href="{{ route('certifications.show', $certification->id) }}" class="btn btn-sm btn-info">
                    <i class="fa fa-eye"></i>
                </a>
                <a href="{{ route('certifications.edit', $certification->id) }}" class="btn btn-sm btn-warning">
                    <i class="fa fa-edit"></i>
                </a>
                <a href="{{ route('certifications.destroy', $certification->id) }}"
                    class="btn btn-danger"
                    onclick="return makeDeleteRequest(event, {{ $certification->id }})">
                    <i class="fa fa-trash"></i>
                    <x-form action="{{ route('certifications.destroy', $certification->id) }}"
                            id="delete-form-{{ $certification->id }}" style="display: none;"
                            method="DELETE">
                    </x-form>
                </a>
            </td>
        </tr>
    @empty
        <tr>
            <td colspan="6" class="text-center">No certifications found.</td>
        </tr>
    @endforelse
    </tbody>
</table>

<div class="d-flex justify-content-center">
    {{ $certifications->links() }}
</div>
