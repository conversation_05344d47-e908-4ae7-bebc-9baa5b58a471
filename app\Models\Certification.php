<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Certification extends Model
{
    use HasFactory;

    protected $guarded = ['id'];

    protected $casts = [
        'certification_date' => 'date',
    ];

    /**
     * Get the user that owns the certification.
     */
    public function owner(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

    /**
     * Get the upwork ID associated with the certification.
     */
    public function upworkID(): BelongsTo
    {
        return $this->belongsTo(UpworkId::class, 'upwork_id', 'id');
    }

    /**
     * Get the file extension from the certification upload path.
     */
    public function getFileExtension(): string
    {
        return pathinfo($this->certification_upload, PATHINFO_EXTENSION);
    }

    /**
     * Check if the certification file is an image.
     */
    public function isImage(): bool
    {
        $imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'];
        return in_array(strtolower($this->getFileExtension()), $imageExtensions);
    }

    /**
     * Check if the certification file is a PDF.
     */
    public function isPdf(): bool
    {
        return strtolower($this->getFileExtension()) === 'pdf';
    }

    /**
     * Check if the certification file is a document.
     */
    public function isDocument(): bool
    {
        $docExtensions = ['doc', 'docx', 'txt', 'rtf'];
        return in_array(strtolower($this->getFileExtension()), $docExtensions);
    }
}
