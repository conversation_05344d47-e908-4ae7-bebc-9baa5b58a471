<x-admin.app :title="$page_title" :from_url="route('admin.certifications.index')">

    <x-slot name="breadcrumbSlot">
        <a href="{{ route('admin.certifications.index') }}" class="btn btn-outline-secondary">
            <i class="fa fa-arrow-left"></i>
            <span>Back to Certifications</span>
        </a>
        <a href="{{ route('admin.certifications.show', $certification->id) }}" class="btn btn-outline-info">
            <i class="fa fa-eye"></i>
            <span>View Certification</span>
        </a>
    </x-slot>

    <div class="card">
        <div class="card-header">
            <h4 class="card-title">{{ $page_title }}</h4>
        </div>
        <div class="card-body">
            <form action="{{ route('admin.certifications.update', $certification->id) }}" method="POST" enctype="multipart/form-data">
                @csrf
                @method('PUT')
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="user_id">User <span class="text-danger">*</span></label>
                            <select class="form-control @error('user_id') is-invalid @enderror" id="user_id" name="user_id" required>
                                <option value="">Select User</option>
                                @foreach($users as $user)
                                    <option value="{{ $user->id }}" {{ (old('user_id') ?? $certification->user_id) == $user->id ? 'selected' : '' }}>
                                        {{ $user->name }}
                                    </option>
                                @endforeach
                            </select>
                            @error('user_id')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="upwork_id">Upwork ID <span class="text-danger">*</span></label>
                            <select class="form-control @error('upwork_id') is-invalid @enderror" id="upwork_id" name="upwork_id" required>
                                <option value="">Select Upwork ID</option>
                                @foreach($upworkIds as $upworkId)
                                    <option value="{{ $upworkId->id }}" {{ (old('upwork_id') ?? $certification->upwork_id) == $upworkId->id ? 'selected' : '' }}>
                                        {{ $upworkId->upwork_id }}
                                    </option>
                                @endforeach
                            </select>
                            @error('upwork_id')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="certification_title">Certification Title</label>
                            <input type="text" class="form-control @error('certification_title') is-invalid @enderror" 
                                   id="certification_title" name="certification_title" 
                                   value="{{ old('certification_title') ?? $certification->certification_title }}" 
                                   placeholder="Enter certification title">
                            @error('certification_title')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="certification_date">Certification Date</label>
                            <input type="date" class="form-control @error('certification_date') is-invalid @enderror" 
                                   id="certification_date" name="certification_date" 
                                   value="{{ old('certification_date') ?? ($certification->certification_date ? $certification->certification_date->format('Y-m-d') : '') }}">
                            @error('certification_date')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-12">
                        <div class="form-group">
                            <label for="certification_upload">Certification File</label>
                            @if($certification->certification_upload)
                                <div class="mb-2">
                                    <strong>Current file:</strong>
                                    <a href="{{ Storage::url($certification->certification_upload) }}" target="_blank" class="btn btn-sm btn-info">
                                        <i class="fa fa-download"></i> View Current File
                                    </a>
                                </div>
                            @endif
                            <input type="file" class="form-control @error('certification_upload') is-invalid @enderror" 
                                   id="certification_upload" name="certification_upload" 
                                   accept=".pdf,.jpg,.jpeg,.png,.doc,.docx">
                            <small class="form-text text-muted">
                                Leave empty to keep current file. Allowed file types: PDF, JPG, PNG, DOC, DOCX. Maximum file size: 10MB.
                            </small>
                            @error('certification_upload')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <button type="submit" class="btn btn-primary">
                        <i class="fa fa-save"></i> Update Certification
                    </button>
                    <a href="{{ route('admin.certifications.index') }}" class="btn btn-secondary">
                        <i class="fa fa-times"></i> Cancel
                    </a>
                </div>
            </form>
        </div>
    </div>

    <x-slot:bottomScripts>
        <script type="text/javascript">
            $(document).ready(function () {
                $('#user_id').select2({
                    placeholder: 'Select User',
                    allowClear: true
                });
                
                $('#upwork_id').select2({
                    placeholder: 'Select Upwork ID',
                    allowClear: true
                });

                // File upload preview
                $('#certification_upload').on('change', function() {
                    const file = this.files[0];
                    if (file) {
                        const fileSize = (file.size / 1024 / 1024).toFixed(2);
                        if (fileSize > 10) {
                            alert('File size must be less than 10MB');
                            $(this).val('');
                        }
                    }
                });
            });
        </script>
    </x-slot>

</x-admin.app>
