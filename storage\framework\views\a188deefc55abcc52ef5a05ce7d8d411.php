<?php if (isset($component)) { $__componentOriginalf56340035eeb7de26edb3ee88689cd6b = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalf56340035eeb7de26edb3ee88689cd6b = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.user.app','data' => ['title' => $page_title,'fromUrl' => route('certifications.index')]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('user.app'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['title' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($page_title),'from_url' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(route('certifications.index'))]); ?>

     <?php $__env->slot('breadcrumbSlot', null, []); ?> 
        <a href="<?php echo e(route('certifications.index')); ?>" class="btn btn-outline-secondary">
            <i class="fa fa-arrow-left"></i>
            <span>Back to Certifications</span>
        </a>
        <a href="<?php echo e(route('certifications.edit', $certification->id)); ?>" class="btn btn-outline-warning">
            <i class="fa fa-edit"></i>
            <span>Edit Certification</span>
        </a>
     <?php $__env->endSlot(); ?>

    <div class="card">
        <div class="card-header">
            <h4 class="card-title"><?php echo e($page_title); ?></h4>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <table class="table table-bordered">
                        <tr>
                            <th width="30%">Upwork ID:</th>
                            <td><?php echo e($certification->upworkID->upwork_id ?? 'N/A'); ?></td>
                        </tr>
                        <tr>
                            <th>Certification Title:</th>
                            <td><?php echo e($certification->certification_title ?? 'N/A'); ?></td>
                        </tr>
                        <tr>
                            <th>Certification Date:</th>
                            <td><?php echo e($certification->certification_date ? $certification->certification_date->format('M d, Y') : 'N/A'); ?></td>
                        </tr>
                        <tr>
                            <th>Created At:</th>
                            <td><?php echo e($certification->created_at->format('M d, Y h:i A')); ?></td>
                        </tr>
                        <tr>
                            <th>Updated At:</th>
                            <td><?php echo e($certification->updated_at->format('M d, Y h:i A')); ?></td>
                        </tr>
                    </table>
                </div>
                
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5>Certification File</h5>
                        </div>
                        <div class="card-body text-center">
                            <?php if($certification->certification_upload): ?>
                                <?php if($certification->isImage()): ?>
                                    <div class="mb-3">
                                        <img src="<?php echo e(Storage::url($certification->certification_upload)); ?>" 
                                             alt="Certification" 
                                             class="img-fluid" 
                                             style="max-height: 300px;">
                                    </div>
                                <?php elseif($certification->isPdf()): ?>
                                    <div class="mb-3">
                                        <i class="fa fa-file-pdf-o fa-5x text-danger"></i>
                                        <p class="mt-2">PDF Document</p>
                                    </div>
                                <?php elseif($certification->isDocument()): ?>
                                    <div class="mb-3">
                                        <i class="fa fa-file-word-o fa-5x text-primary"></i>
                                        <p class="mt-2">Document File</p>
                                    </div>
                                <?php else: ?>
                                    <div class="mb-3">
                                        <i class="fa fa-file-o fa-5x text-secondary"></i>
                                        <p class="mt-2">File</p>
                                    </div>
                                <?php endif; ?>
                                
                                <div class="btn-group">
                                    <a href="<?php echo e(Storage::url($certification->certification_upload)); ?>" 
                                       target="_blank" 
                                       class="btn btn-primary">
                                        <i class="fa fa-eye"></i> View File
                                    </a>
                                    <a href="<?php echo e(Storage::url($certification->certification_upload)); ?>" 
                                       download 
                                       class="btn btn-success">
                                        <i class="fa fa-download"></i> Download
                                    </a>
                                </div>
                                
                                <div class="mt-3">
                                    <small class="text-muted">
                                        File Type: <?php echo e(strtoupper($certification->getFileExtension())); ?>

                                    </small>
                                </div>
                            <?php else: ?>
                                <div class="text-center">
                                    <i class="fa fa-file-o fa-5x text-muted"></i>
                                    <p class="mt-2 text-muted">No file uploaded</p>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="row mt-4">
                <div class="col-md-12">
                    <div class="btn-group">
                        <a href="<?php echo e(route('certifications.edit', $certification->id)); ?>" class="btn btn-warning">
                            <i class="fa fa-edit"></i> Edit Certification
                        </a>
                        <form action="<?php echo e(route('certifications.destroy', $certification->id)); ?>" method="POST" style="display: inline-block;">
                            <?php echo csrf_field(); ?>
                            <?php echo method_field('DELETE'); ?>
                            <button type="submit" class="btn btn-danger" onclick="return confirm('Are you sure you want to delete this certification?')">
                                <i class="fa fa-trash"></i> Delete Certification
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalf56340035eeb7de26edb3ee88689cd6b)): ?>
<?php $attributes = $__attributesOriginalf56340035eeb7de26edb3ee88689cd6b; ?>
<?php unset($__attributesOriginalf56340035eeb7de26edb3ee88689cd6b); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalf56340035eeb7de26edb3ee88689cd6b)): ?>
<?php $component = $__componentOriginalf56340035eeb7de26edb3ee88689cd6b; ?>
<?php unset($__componentOriginalf56340035eeb7de26edb3ee88689cd6b); ?>
<?php endif; ?>
<?php /**PATH D:\xampp8.2\htdocs\portfolio\resources\views/user/certifications/show.blade.php ENDPATH**/ ?>