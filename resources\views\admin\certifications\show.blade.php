<x-admin.app :title="$page_title" :from_url="route('admin.certifications.index')">

    <x-slot name="breadcrumbSlot">
        <a href="{{ route('admin.certifications.index') }}" class="btn btn-outline-secondary">
            <i class="fa fa-arrow-left"></i>
            <span>Back to Certifications</span>
        </a>
        <a href="{{ route('admin.certifications.edit', $certification->id) }}" class="btn btn-outline-warning">
            <i class="fa fa-edit"></i>
            <span>Edit Certification</span>
        </a>
    </x-slot>

    <div class="card">
        <div class="card-header">
            <h4 class="card-title">{{ $page_title }}</h4>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <table class="table table-bordered">
                        <tr>
                            <th width="30%">User:</th>
                            <td>{{ $certification->owner->name ?? 'N/A' }}</td>
                        </tr>
                        <tr>
                            <th>Upwork ID:</th>
                            <td>{{ $certification->upworkID->upwork_id ?? 'N/A' }}</td>
                        </tr>
                        <tr>
                            <th>Certification Title:</th>
                            <td>{{ $certification->certification_title ?? 'N/A' }}</td>
                        </tr>
                        <tr>
                            <th>Certification Date:</th>
                            <td>{{ $certification->certification_date ? $certification->certification_date->format('M d, Y') : 'N/A' }}</td>
                        </tr>
                        <tr>
                            <th>Created At:</th>
                            <td>{{ $certification->created_at->format('M d, Y h:i A') }}</td>
                        </tr>
                        <tr>
                            <th>Updated At:</th>
                            <td>{{ $certification->updated_at->format('M d, Y h:i A') }}</td>
                        </tr>
                    </table>
                </div>
                
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5>Certification File</h5>
                        </div>
                        <div class="card-body text-center">
                            @if($certification->certification_upload)
                                @if($certification->isImage())
                                    <div class="mb-3">
                                        <img src="{{ Storage::url($certification->certification_upload) }}" 
                                             alt="Certification" 
                                             class="img-fluid" 
                                             style="max-height: 300px;">
                                    </div>
                                @elseif($certification->isPdf())
                                    <div class="mb-3">
                                        <i class="fa fa-file-pdf-o fa-5x text-danger"></i>
                                        <p class="mt-2">PDF Document</p>
                                    </div>
                                @elseif($certification->isDocument())
                                    <div class="mb-3">
                                        <i class="fa fa-file-word-o fa-5x text-primary"></i>
                                        <p class="mt-2">Document File</p>
                                    </div>
                                @else
                                    <div class="mb-3">
                                        <i class="fa fa-file-o fa-5x text-secondary"></i>
                                        <p class="mt-2">File</p>
                                    </div>
                                @endif
                                
                                <div class="btn-group">
                                    <a href="{{ Storage::url($certification->certification_upload) }}" 
                                       target="_blank" 
                                       class="btn btn-primary">
                                        <i class="fa fa-eye"></i> View File
                                    </a>
                                    <a href="{{ Storage::url($certification->certification_upload) }}" 
                                       download 
                                       class="btn btn-success">
                                        <i class="fa fa-download"></i> Download
                                    </a>
                                </div>
                                
                                <div class="mt-3">
                                    <small class="text-muted">
                                        File Type: {{ strtoupper($certification->getFileExtension()) }}
                                    </small>
                                </div>
                            @else
                                <div class="text-center">
                                    <i class="fa fa-file-o fa-5x text-muted"></i>
                                    <p class="mt-2 text-muted">No file uploaded</p>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="row mt-4">
                <div class="col-md-12">
                    <div class="btn-group">
                        <a href="{{ route('admin.certifications.edit', $certification->id) }}" class="btn btn-warning">
                            <i class="fa fa-edit"></i> Edit Certification
                        </a>
                        <form action="{{ route('admin.certifications.destroy', $certification->id) }}" method="POST" style="display: inline-block;">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="btn btn-danger" onclick="return confirm('Are you sure you want to delete this certification?')">
                                <i class="fa fa-trash"></i> Delete Certification
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

</x-admin.app>
